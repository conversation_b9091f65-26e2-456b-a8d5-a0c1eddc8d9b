<template>
  <div class="app-container">
    <!-- 库存筛选 -->
    <el-card shadow="never" class="box-card form-card mb10">
      <div class="filter-header">
        <span class="filter-title">库存筛选</span>
      </div>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" class="form_box">
        <el-row class="form_row">
          <el-row class="form_col">
            <el-form-item label="仓库选择" prop="warehouseId">
              <el-select v-model="queryParams.warehouseId" placeholder="全部" clearable style="width: 200px;">
                <el-option v-for="item in warehouseList" :key="item.warehouseId" :label="item.warehouseName"
                  :value="item.warehouseId">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="入库时间" prop="dateRange" style="margin-left: 20px;">
              <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd" style="width: 300px;">
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <template v-if="toggleSearchDom">
                <el-button type="text" @click="packUp">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <i :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"></i>
                </el-button>
              </template>
            </el-form-item>
          </el-row>
        </el-row>
      </el-form>
    </el-card>

    <!-- 仓库分布图 -->
    <el-card shadow="never" class="mb10">
      <WarehouseDistribution :warehouses="warehouseDistribution" @warehouse-click="handleWarehouseClick" />
    </el-card>

    <!-- 库存总量和批次分布 -->
    <el-card shadow="never" class="mb10 chatbox">
      <InventorySummary :summaryData="inventorySummaryData" :batchData="batchDistributionData" />
    </el-card>

    <!-- 库存明细表格 -->
    <el-card shadow="never" class="table_box card_radius_b">
      <InventoryTable :tableData="inventoryTableData" :loading="tableLoading" :pagination="tablePagination"
        :warehouseList="warehouseList" :filterParams="queryParams" @onPageChange="handlePageChange"
        @onPageSizeChange="handlePageSizeChange" @onQuery="handleTableQuery" @onDetail="handleDetail" />
    </el-card>

    <!-- 入库详情弹窗 -->
    <el-drawer
      class="drawer_box"
      :visible.sync="detailVisible"
      :show-close="true"
      :append-to-body="true"
      :destroy-on-close="true"
      size="80%"
      :title="getDetailTitle(currentDetailItem)"
      :wrapperClosable="false">
      <InWarehouseDetail ref="inWarehouseDetail" :dataInfo="currentDetailItem"></InWarehouseDetail>
    </el-drawer>

  </div>
</template>

<script>
import { tableUi } from "@/utils/mixin/tableUi.js";
import { warehouseList } from "@/api/basics/index.js";
import { selectTotalNum, selectInventoryList, listOfInventoryLevel, inventoryPage2 } from "@/api/board.js";
import WarehouseDistribution from './components/WarehouseDistribution.vue';
import InventorySummary from './components/InventorySummary.vue';
import InventoryTable from './components/InventoryTable.vue';
import InWarehouseDetail from '../components/inWarehouseDetail.vue';

export default {
  mixins: [tableUi],
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        warehouseId: '',
        startTime: '',
        endTime: ''
      },
      warehouseList: [], // 仓库列表
      dateRange: [], // 日期范围

      // 仓库分布数据
      warehouseDistribution: [],

      // 库存总量数据
      inventorySummaryData: {},

      // 批次分布数据
      batchDistributionData: {},
      // 表格数据
      inventoryTableData: [],
      tableLoading: false,
      tablePagination: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },

      // 详情弹窗相关
      detailVisible: false,
      currentDetailItem: {}
    };
  },
  components: {
    WarehouseDistribution,
    InventorySummary,
    InventoryTable,
    InWarehouseDetail
  },
  created() {
    this.getWarehouseList();
    this.getWarehouseDistribution();
    this.getAllData();
  },
  methods: {
    // 获取仓库列表
    async getWarehouseList() {
      try {
        const res = await warehouseList({ pageNum: 1, pageSize: 10000, warehouseType: '' });
        if (res.code === 200) {
          this.warehouseList = res.result.list || [];
        }
      } catch (error) {
        console.error('获取仓库列表失败:', error);
      }
    },

    // 获取所有数据
    async getAllData() {
      await Promise.all([
        this.getInventorySummary(),
        this.getBatchDistribution(),
        this.getInventoryTableData()
      ]);
    },

    // 获取仓库分布数据
    async getWarehouseDistribution() {
      const res = await listOfInventoryLevel({ warehouseType: '' })
      console.log('🚀🚀仓库分布数据', res)
      this.warehouseDistribution = res.result
    },

    // 获取库存总量数据
    async getInventorySummary() {
      try {
        const params = {
          warehouseId: this.queryParams.warehouseId || null,
          startTime: this.queryParams.startTime || null,
          endTime: this.queryParams.endTime || null
        };
        const res = await selectTotalNum(params);
        const { inCnt, inWeightNum, storeAgeList } = res.result;
        const [, { storeAgeNum: lowInventory } = {}, { storeAgeNum: stagnantInventory } = {}] = storeAgeList;
        if (res.code == 200) {
          this.inventorySummaryData = {
            inCnt,
            inWeightNum,
            lowInventory,
            stagnantInventory,
            storeAgeList
          }
        }
      } catch (error) {
        console.error('获取库存总量数据失败:', error);
      }
    },

    // 获取批次分布数据
    async getBatchDistribution() {
      try {
        const params = {
          warehouseId: this.queryParams.warehouseId || null,
          startTime: this.queryParams.startTime || null,
          endTime: this.queryParams.endTime || null
        };

        const res = await selectInventoryList(params);
        if (res.code == 200 && res.result) {
          /*  this.batchDistributionData = {
             "inCnt": null,
             "inWeightNum": null,
             "storeAgeList": null,
             "batchList": [
               {
                 "batchType": 1,
                 "batchNum": 987,
                 "batchPercent": 342,
                 "monthDiff": null
               },
               {
                 "batchType": 2,
                 "batchNum": 634,
                 "batchPercent": 654,
                 "monthDiff": null
               },
               {
                 "batchType": 3,
                 "batchNum": 564,
                 "batchPercent": 456,
                 "monthDiff": null
               },
               {
                 "batchType": 4,
                 "batchNum": 351,
                 "batchPercent": 153,
                 "monthDiff": null
               }
             ]
           }; */
          this.batchDistributionData = res.result
          console.log('🚀🚀🚀获取批次分布数据', this.batchDistributionData);
        }
      } catch (error) {
        console.error('获取批次分布数据失败:', error);
      }
    },

    // 获取库存表格数据
    async getInventoryTableData(params = {}) {
      this.tableLoading = true;
      try {
        const queryParams = {
          pageNum: this.tablePagination.pageNum,
          pageSize: this.tablePagination.pageSize,
          ...this.queryParams,
          ...params
        };

        const res = await inventoryPage2(queryParams);
        if (res.code == 200 && res.result) {
          const { list, total } = res.result;

          // 转换数据格式以适配表格显示
          this.inventoryTableData = (list || []).map(item => ({
            inventoryId: item.inventoryId,
            inventoryCode: item.inventoryCode,
            inventoryType: item.inventoryType,
            warehouseId: item.warehouseId,
            warehouseName: item.warehouseName,
            inventoryNum: item.inventoryNum || 0,
            inventoryWeight: parseFloat(item.inventoryWeight) || 0,
            createUserName: item.createUserName,
            createTime: item.createTime,
            storeAgeNum: item.storeAgeNum,
            storeAgeType: item.storeAgeType
          }));

          this.tablePagination.total = parseInt(total) || 0;
        }
      } catch (error) {
        console.error('获取表格数据失败:', error);
        this.inventoryTableData = [];
        this.tablePagination.total = 0;
      } finally {
        this.tableLoading = false;
      }
    },

    // 计算库存天数（简单逻辑，可根据实际需求调整）
    calculateStockLevel(inventoryNum) {
      if (inventoryNum > 100) return '90天以上';
      if (inventoryNum > 50) return '60-90天';
      if (inventoryNum > 20) return '30-60天';
      return '30天以下';
    },

    // 查询
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.tablePagination.pageNum = 1;
      this.handleDateRange();
      this.getAllData();
    },

    // 重置
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        warehouseId: '',
        startTime: '',
        endTime: ''
      };
      this.tablePagination.pageNum = 1;
      this.dateRange = [];
      this.handleQuery();
    },

    // 处理日期范围
    handleDateRange() {
      if (this.dateRange && this.dateRange.length === 2) {
        this.queryParams.startTime = this.dateRange[0];
        this.queryParams.endTime = this.dateRange[1];
      } else {
        this.queryParams.startTime = '';
        this.queryParams.endTime = '';
      }
    },

    // 仓库点击事件
    handleWarehouseClick(warehouse) {
      console.log('点击仓库:', warehouse);
      // 可以在这里添加仓库点击后的逻辑，比如筛选该仓库的库存数据
      // this.queryParams.warehouseId = warehouse.warehouseId;
      // this.handleQuery();
    },

    // 表格查询
    handleTableQuery(params) {
      this.tablePagination.pageNum = 1;
      this.getInventoryTableData(params);
    },

    // 表格分页
    handlePageChange(page) {
      this.tablePagination.pageNum = page;
      this.getInventoryTableData();
    },

    handlePageSizeChange(size) {
      this.tablePagination.pageSize = size;
      this.tablePagination.pageNum = 1;
      this.getInventoryTableData();
    },

    // 查看详情
    handleDetail(row) {
      console.log('查看详情:', row);
      this.currentDetailItem = row;
      this.detailVisible = true;
    },

    // 获取详情标题
    getDetailTitle(item) {
      if (!item || !item.inventoryType) return '入库详情';
      const typeMap = {
        '11': '急冻入库',
        '12': '成品入库',
        '13': '结余入库',
        '14': '盘盈入库'
      };
      return typeMap[item.inventoryType] || '入库详情';
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 10px;
  background: #F7F8FA;
  min-height: calc(100vh - 84px);
}

.filter-header {
  margin-bottom: 20px;

  .filter-title {
    font-size: 16px;
    font-weight: 500;
    color: #1D2129;
    position: relative;
    padding-left: 12px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background: #165DFF;
      border-radius: 2px;
    }
  }
}

.el-card {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);

  &.mb10 {
    margin-bottom: 16px;
  }


  ::v-deep .el-card__body {
    padding: 24px;
  }
}

.chatbox {
  background-color: #F7F8FA;

  ::v-deep .el-card__body {
    padding: 0 !important;
  }
}

.form_box {
  .el-form-item {
    margin-bottom: 16px;

    .el-form-item__label {
      font-weight: 500;
      color: #1D2129;
    }
  }

  .el-select,
  .el-input,
  .el-date-editor {
    .el-input__inner {
      border: 1px solid #E5E6EB;
      border-radius: 6px;

      &:focus {
        border-color: #165DFF;
        box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.1);
      }
    }
  }

  .el-button {
    border-radius: 6px;
    font-weight: 500;

    &.el-button--primary {
      background: #165DFF;
      border-color: #165DFF;

      &:hover {
        background: #4080FF;
        border-color: #4080FF;
      }
    }

    &.el-button--default {
      color: #4E5969;
      border-color: #E5E6EB;

      &:hover {
        color: #165DFF;
        border-color: #165DFF;
      }
    }
  }
}

// 响应式布局
@media (max-width: 1200px) {
  .warehouse-grid {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

@media (max-width: 768px) {
  .warehouse-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .el-col {
    margin-bottom: 16px;
  }
}
</style>